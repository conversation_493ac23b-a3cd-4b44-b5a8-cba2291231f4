@import "tailwindcss";

:root {
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary: #f59e0b;
  --accent: #8b5cf6;
  --background: #ffffff;
  --foreground: #171717;
  --muted: #f8fafc;
  --border: #e2e8f0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.7;
  font-size: 16px;
}

/* ULTRA ENHANCED Typography - Maximum Impact Sizes */
h1, h2, h3, h4, h5, h6 {
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 3rem;
  letter-spacing: -0.02em;
}

h1 {
  font-size: 6rem;
  line-height: 1.0;
}

h2 {
  font-size: 5rem;
  line-height: 1.1;
}

h3 {
  font-size: 4rem;
  line-height: 1.2;
}

h4 {
  font-size: 3rem;
  line-height: 1.3;
}

p {
  font-size: 1.75rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
}

/* Ultra Large text for maximum readability */
.text-large {
  font-size: 2.5rem;
  line-height: 1.7;
}

.text-xl-custom {
  font-size: 3rem;
  line-height: 1.6;
}

.text-xxl-custom {
  font-size: 3.5rem;
  line-height: 1.5;
}

/* Maximum spacing for full display utilization */
.section-padding {
  padding-top: 10rem;
  padding-bottom: 10rem;
}

@media (min-width: 640px) {
  h1 {
    font-size: 7rem;
  }

  h2 {
    font-size: 6rem;
  }

  h3 {
    font-size: 5rem;
  }

  h4 {
    font-size: 4rem;
  }

  p {
    font-size: 2rem;
  }

  .text-large {
    font-size: 3rem;
  }

  .text-xl-custom {
    font-size: 3.5rem;
  }

  .text-xxl-custom {
    font-size: 4rem;
  }
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 14rem;
    padding-bottom: 14rem;
  }

  h1 {
    font-size: 9rem;
  }

  h2 {
    font-size: 7.5rem;
  }

  h3 {
    font-size: 6rem;
  }

  h4 {
    font-size: 5rem;
  }

  p {
    font-size: 2.25rem;
  }

  .text-large {
    font-size: 3.5rem;
  }

  .text-xl-custom {
    font-size: 4rem;
  }

  .text-xxl-custom {
    font-size: 4.5rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 18rem;
    padding-bottom: 18rem;
  }

  h1 {
    font-size: 11rem;
  }

  h2 {
    font-size: 9rem;
  }

  h3 {
    font-size: 7rem;
  }

  h4 {
    font-size: 6rem;
  }

  p {
    font-size: 2.5rem;
  }

  .text-large {
    font-size: 4rem;
  }

  .text-xl-custom {
    font-size: 4.5rem;
  }

  .text-xxl-custom {
    font-size: 5rem;
  }
}

@media (min-width: 1280px) {
  .section-padding {
    padding-top: 22rem;
    padding-bottom: 22rem;
  }

  h1 {
    font-size: 13rem;
  }

  h2 {
    font-size: 11rem;
  }

  h3 {
    font-size: 9rem;
  }

  h4 {
    font-size: 7rem;
  }

  p {
    font-size: 2.75rem;
  }

  .text-large {
    font-size: 4.5rem;
  }

  .text-xl-custom {
    font-size: 5rem;
  }

  .text-xxl-custom {
    font-size: 5.5rem;
  }
}

@media (min-width: 1536px) {
  .section-padding {
    padding-top: 26rem;
    padding-bottom: 26rem;
  }

  h1 {
    font-size: 15rem;
  }

  h2 {
    font-size: 13rem;
  }

  h3 {
    font-size: 11rem;
  }

  h4 {
    font-size: 9rem;
  }

  p {
    font-size: 3rem;
  }

  .text-large {
    font-size: 5rem;
  }

  .text-xl-custom {
    font-size: 5.5rem;
  }

  .text-xxl-custom {
    font-size: 6rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
}

.gradient-tech {
  background: linear-gradient(135deg, #1e1b4b 0%, #312e81 25%, #3730a3 50%, #4338ca 75%, #4f46e5 100%);
}

/* ULTRA ENHANCED Glass morphism effect - Professional Quality */
.glass {
  background: rgba(255, 255, 255, 0.12);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-ultra {
  background: rgba(255, 255, 255, 0.08);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    0 0 40px rgba(34, 211, 238, 0.1);
}

.glass-neon {
  background: rgba(255, 255, 255, 0.06);
  -webkit-backdrop-filter: blur(35px);
  backdrop-filter: blur(35px);
  border: 2px solid rgba(34, 211, 238, 0.3);
  box-shadow:
    0 35px 70px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    0 0 50px rgba(34, 211, 238, 0.2),
    0 0 100px rgba(147, 51, 234, 0.1);
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-blue-500/25;
}

.btn-secondary {
  @apply bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300;
}

.btn-glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300;
}

/* Responsive Design Utilities */
@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem !important;
    line-height: 1.1 !important;
  }

  .hero-subtitle {
    font-size: 1.125rem !important;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 2.5rem !important;
  }

  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Enhanced Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Gradient Text Animation */
.gradient-text-animated {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Loading Spinner */
.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern blob animation for unified layout */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Unified seamless background system */
.unified-background {
  background: linear-gradient(
    135deg,
    #0f0f23 0%,
    #1a1a3e 15%,
    #2d1b69 30%,
    #4c1d95 45%,
    #7c2d12 60%,
    #dc2626 75%,
    #ec4899 90%,
    #8b5cf6 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Remove container constraints for full-width */
.full-width-content {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  padding-left: calc(50vw - 50%);
  padding-right: calc(50vw - 50%);
}

/* Animation delay classes */
.animate-delay-1 {
  animation-delay: 1s;
}

.animate-delay-2 {
  animation-delay: 2s;
}

.animate-delay-3 {
  animation-delay: 3s;
}

.animate-delay-0-5 {
  animation-delay: 0.5s;
}

.animate-delay-1-5 {
  animation-delay: 1.5s;
}

.animate-delay-2-5 {
  animation-delay: 2.5s;
}

.animate-delay-4 {
  animation-delay: 4s;
}

/* Ultra large text sizes for massive impact */
.text-ultra {
  font-size: 15rem;
  line-height: 0.8;
}

.text-mega {
  font-size: 12rem;
  line-height: 0.85;
}

.text-giant {
  font-size: 10rem;
  line-height: 0.9;
}

/* Enhanced neon glow effects */
.neon-glow {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 40px currentColor;
}

.neon-glow-strong {
  text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 80px currentColor, 0 0 120px currentColor;
}

/* AMPD Brand Specific Glows */
.ampd-glow {
  text-shadow:
    0 0 8px rgba(34, 211, 238, 0.6),
    0 0 16px rgba(147, 51, 234, 0.4),
    0 0 24px rgba(34, 211, 238, 0.3);
}

.deviq-glow {
  text-shadow:
    0 0 6px rgba(255, 255, 255, 0.8),
    0 0 12px rgba(147, 51, 234, 0.5),
    0 0 18px rgba(34, 211, 238, 0.3);
}

/* Enhanced Brand Animation */
.brand-pulse {
  animation: brandPulse 3s ease-in-out infinite;
}

@keyframes brandPulse {
  0%, 100% {
    text-shadow:
      0 0 8px rgba(34, 211, 238, 0.6),
      0 0 16px rgba(147, 51, 234, 0.4);
  }
  50% {
    text-shadow:
      0 0 12px rgba(34, 211, 238, 0.8),
      0 0 24px rgba(147, 51, 234, 0.6),
      0 0 36px rgba(34, 211, 238, 0.4);
  }
}

/* Responsive ultra large text */
@media (min-width: 1536px) {
  .text-ultra {
    font-size: 20rem;
  }
  .text-mega {
    font-size: 16rem;
  }
}

@media (max-width: 1024px) {
  .text-ultra {
    font-size: 10rem;
  }
  .text-mega {
    font-size: 8rem;
  }
  .text-giant {
    font-size: 6rem;
  }
}

@media (max-width: 768px) {
  .text-ultra {
    font-size: 6rem;
  }
  .text-mega {
    font-size: 5rem;
  }
  .text-giant {
    font-size: 4rem;
  }
}

@media (max-width: 640px) {
  .text-ultra {
    font-size: 4rem;
  }
  .text-mega {
    font-size: 3.5rem;
  }
  .text-giant {
    font-size: 3rem;
  }
}

/* Ring Roadmap Specific Styles */
.ring-roadmap-container {
  position: relative;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  aspect-ratio: 1;
}

.ring-step-card {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.ring-step-card:hover {
  transform: translateY(-12px) scale(1.05);
  box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
}

.ring-connecting-line {
  background: linear-gradient(
    to bottom,
    rgba(251, 146, 60, 0.8) 0%,
    rgba(147, 51, 234, 0.6) 50%,
    rgba(251, 146, 60, 0.8) 100%
  );
  box-shadow: 0 0 10px rgba(251, 146, 60, 0.3);
}

/* Enhanced Content Alignment */
.content-section {
  padding: 6rem 0;
  position: relative;
}

@media (min-width: 768px) {
  .content-section {
    padding: 8rem 0;
  }
}

@media (min-width: 1024px) {
  .content-section {
    padding: 10rem 0;
  }
}

/* Better responsive grid layouts */
.responsive-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 4rem;
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    gap: 5rem;
  }
}

/* Enhanced card layouts for better alignment */
.enhanced-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  padding: 2.5rem;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-card:hover::before {
  opacity: 1;
}

.enhanced-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(34, 211, 238, 0.4);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 30px rgba(34, 211, 238, 0.2);
}

/* Better text alignment and spacing */
.text-content {
  max-width: none;
  line-height: 1.8;
}

.text-content h1,
.text-content h2,
.text-content h3 {
  margin-bottom: 1.5rem;
}

.text-content p {
  margin-bottom: 2rem;
}

/* Improved button alignment */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
}

@media (min-width: 768px) {
  .button-group {
    gap: 2rem;
  }
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .ring-roadmap-container {
    display: none;
  }

  .mobile-roadmap {
    display: block;
  }

  .enhanced-card {
    padding: 3rem;
  }

  .text-content {
    text-align: center;
  }

  /* Mobile specific sizing */
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  h1 {
    font-size: 4rem !important;
  }

  h2 {
    font-size: 3rem !important;
  }

  h3 {
    font-size: 2.5rem !important;
  }

  p {
    font-size: 1.25rem !important;
  }
}

@media (min-width: 769px) {
  .mobile-roadmap {
    display: none;
  }
}

/* Better container system */
.full-width-content {
  width: 100%;
  max-width: none;
  padding: 0 2rem;
}

@media (min-width: 768px) {
  .full-width-content {
    padding: 0 4rem;
  }
}

@media (min-width: 1024px) {
  .full-width-content {
    padding: 0 6rem;
  }
}

@media (min-width: 1280px) {
  .full-width-content {
    padding: 0 8rem;
  }
}

@media (min-width: 1536px) {
  .full-width-content {
    padding: 0 10rem;
  }
}

/* Improved spacing system */
.section-spacing {
  margin-bottom: 8rem;
}

@media (min-width: 768px) {
  .section-spacing {
    margin-bottom: 12rem;
  }
}

@media (min-width: 1024px) {
  .section-spacing {
    margin-bottom: 16rem;
  }
}

/* ULTRA ENHANCED Button System - Maximum Impact */
.btn-primary {
  padding: 2rem 4rem;
  font-size: 1.5rem;
  font-weight: 800;
  border-radius: 1.25rem;
  background: linear-gradient(135deg, #22d3ee, #a855f7, #f97316);
  background-size: 200% 200%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(34, 211, 238, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.05);
  background-position: 100% 100%;
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.25),
    0 0 50px rgba(34, 211, 238, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

@media (min-width: 640px) {
  .btn-primary {
    padding: 2.25rem 4.5rem;
    font-size: 1.625rem;
    border-radius: 1.375rem;
  }
}

@media (min-width: 768px) {
  .btn-primary {
    padding: 2.5rem 5rem;
    font-size: 1.75rem;
    border-radius: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .btn-primary {
    padding: 3rem 6rem;
    font-size: 2rem;
    border-radius: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .btn-primary {
    padding: 3.5rem 7rem;
    font-size: 2.25rem;
    border-radius: 2rem;
  }
}

@media (min-width: 1536px) {
  .btn-primary {
    padding: 4rem 8rem;
    font-size: 2.5rem;
    border-radius: 2.25rem;
  }
}

/* Secondary Button Variant */
.btn-secondary-pro {
  padding: 2rem 4rem;
  font-size: 1.5rem;
  font-weight: 700;
  border-radius: 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 15px 30px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.btn-secondary-pro:hover {
  transform: translateY(-2px) scale(1.03);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(34, 211, 238, 0.5);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.2),
    0 0 40px rgba(34, 211, 238, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

@media (min-width: 768px) {
  .btn-secondary-pro {
    padding: 2.5rem 5rem;
    font-size: 1.75rem;
    border-radius: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .btn-secondary-pro {
    padding: 3rem 6rem;
    font-size: 2rem;
    border-radius: 1.75rem;
  }
}

/* Enhanced Particle Animation */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) translateX(-5px);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) translateX(-10px);
    opacity: 0.8;
  }
}

.animate-particle-float {
  animation: particleFloat 8s ease-in-out infinite;
}

/* Enhanced Neon Border Animation */
@keyframes neonBorder {
  0%, 100% {
    border-color: rgba(34, 211, 238, 0.3);
    box-shadow: 0 0 20px rgba(34, 211, 238, 0.2);
  }
  50% {
    border-color: rgba(147, 51, 234, 0.5);
    box-shadow: 0 0 40px rgba(147, 51, 234, 0.3);
  }
}

.animate-neon-border {
  animation: neonBorder 3s ease-in-out infinite;
}

/* Enhanced Background Pulse */
@keyframes backgroundPulse {
  0%, 100% {
    background-size: 100% 100%;
  }
  50% {
    background-size: 110% 110%;
  }
}

.animate-bg-pulse {
  animation: backgroundPulse 6s ease-in-out infinite;
}

/* Enhanced Text Glow Animation */
@keyframes textGlow {
  0%, 100% {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor;
  }
  50% {
    text-shadow:
      0 0 20px currentColor,
      0 0 40px currentColor,
      0 0 60px currentColor,
      0 0 80px currentColor;
  }
}

.animate-text-glow {
  animation: textGlow 4s ease-in-out infinite;
}

/* Enhanced Card Hover with Glow */
.enhanced-card-glow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card-glow:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 30px 60px -12px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(34, 211, 238, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(34, 211, 238, 0.6);
}

/* Professional Image Overlay Effects */
.image-overlay-tech {
  position: relative;
  overflow: hidden;
}

.image-overlay-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(34, 211, 238, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 50%,
    rgba(251, 146, 60, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-overlay-tech:hover::before {
  opacity: 1;
}

/* Enhanced Section Dividers */
.section-divider {
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(34, 211, 238, 0.5) 25%,
    rgba(147, 51, 234, 0.8) 50%,
    rgba(251, 146, 60, 0.5) 75%,
    transparent 100%
  );
  margin: 4rem 0;
}

/* Enhanced Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 640px) {
  .enhanced-card {
    padding: 2rem;
    margin-bottom: 2rem;
  }

  .neon-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor;
  }

  .animate-blob {
    animation-duration: 10s;
  }
}

/* Advanced Morphing Animations */
@keyframes morphShape {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate3d(0, 0, 0) rotateZ(0deg);
  }
  34% {
    border-radius: 70% 60% 70% 30% / 50% 60% 30% 60%;
    transform: translate3d(5px, -10px, 0) rotateZ(-5deg);
  }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    transform: translate3d(-5px, 10px, 0) rotateZ(5deg);
  }
}

.animate-morph {
  animation: morphShape 8s ease-in-out infinite;
}

/* Liquid Button Effects */
@keyframes liquidWave {
  0%, 100% {
    clip-path: polygon(0% 45%, 15% 44%, 32% 50%, 54% 60%, 70% 61%, 84% 59%, 100% 52%, 100% 100%, 0% 100%);
  }
  50% {
    clip-path: polygon(0% 60%, 16% 65%, 34% 66%, 51% 62%, 67% 50%, 84% 45%, 100% 46%, 100% 100%, 0% 100%);
  }
}

.liquid-effect {
  position: relative;
  overflow: hidden;
}

.liquid-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(34, 211, 238, 0.3), rgba(168, 85, 247, 0.3));
  animation: liquidWave 3s ease-in-out infinite;
  z-index: -1;
}

/* 3D Card Hover Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.card-3d:hover {
  transform: rotateY(10deg) rotateX(10deg) scale(1.05);
}

.card-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-3d:hover::before {
  opacity: 1;
}

/* Holographic Text Effect */
@keyframes holographic {
  0%, 100% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(180deg);
  }
}

.holographic-text {
  background: linear-gradient(
    45deg,
    #ff0000, #ff7300, #fffb00, #48ff00,
    #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000
  );
  background-size: 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographic 3s linear infinite;
}

/* Glitch Animation Enhancement */
@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: skew(0deg, 0deg);
  }
  15%, 49% {
    transform: skew(5deg, 0deg);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: skew(0deg, 0deg);
  }
  21%, 62% {
    transform: skew(-3deg, 0deg);
  }
}

.glitch-effect {
  position: relative;
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-effect::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0040;
  z-index: -1;
}

.glitch-effect::after {
  animation: glitch-2 0.5s infinite;
  color: #00ffff;
  z-index: -2;
}

/* Magnetic Button Effect */
.magnetic-button {
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.320, 1);
  cursor: pointer;
}

.magnetic-button:hover {
  transform: translate3d(0, -8px, 0);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Neon Border Pulse */
@keyframes neonPulse {
  0%, 100% {
    box-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  50% {
    box-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor,
      0 0 40px currentColor;
  }
}

.neon-border {
  animation: neonPulse 2s ease-in-out infinite;
}

/* Floating Animation for Elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Advanced Gradient Animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-shift {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

/* Typewriter Effect */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}

/* Professional Infinite Loop Animation for GIFs */
@keyframes infinite-loop {
  0% {
    transform: translateX(0) scale(1);
    opacity: 0.2;
  }
  25% {
    transform: translateX(-2px) scale(1.01);
    opacity: 0.25;
  }
  50% {
    transform: translateX(0) scale(1.02);
    opacity: 0.3;
  }
  75% {
    transform: translateX(2px) scale(1.01);
    opacity: 0.25;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.2;
  }
}

/* ULTRA PROFESSIONAL Card System - Maximum Impact & Perfect Alignment */
.professional-card {
  background: rgba(255, 255, 255, 0.06);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 2.5rem;
  padding: 3rem;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  width: 100%;
  height: auto;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.professional-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(34, 211, 238, 0.05) 0%,
    rgba(147, 51, 234, 0.05) 50%,
    rgba(251, 146, 60, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.professional-card:hover::before {
  opacity: 1;
}

@media (min-width: 640px) {
  .professional-card {
    padding: 3.5rem;
    min-height: 550px;
  }
}

@media (min-width: 768px) {
  .professional-card {
    padding: 4rem;
    min-height: 600px;
  }
}

@media (min-width: 1024px) {
  .professional-card {
    padding: 4.5rem;
    min-height: 650px;
  }
}

@media (min-width: 1280px) {
  .professional-card {
    padding: 5rem;
    min-height: 700px;
  }
}

@media (min-width: 1536px) {
  .professional-card {
    padding: 5.5rem;
    min-height: 750px;
  }
}

.professional-card:hover {
  transform: translateY(-15px) scale(1.04);
  border-color: rgba(34, 211, 238, 0.6);
  box-shadow:
    0 50px 100px rgba(0, 0, 0, 0.3),
    0 0 80px rgba(34, 211, 238, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* Ultra Large Card Variant */
.professional-card-xl {
  background: rgba(255, 255, 255, 0.04);
  -webkit-backdrop-filter: blur(35px);
  backdrop-filter: blur(35px);
  border: 4px solid rgba(255, 255, 255, 0.15);
  border-radius: 3rem;
  padding: 4rem;
  min-height: 600px;
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    0 0 60px rgba(34, 211, 238, 0.15);
}

@media (min-width: 768px) {
  .professional-card-xl {
    padding: 5rem;
    min-height: 700px;
  }
}

@media (min-width: 1024px) {
  .professional-card-xl {
    padding: 6rem;
    min-height: 800px;
  }
}

@media (min-width: 1280px) {
  .professional-card-xl {
    padding: 7rem;
    min-height: 900px;
  }
}

.professional-card-xl:hover {
  transform: translateY(-20px) scale(1.05);
  border-color: rgba(34, 211, 238, 0.7);
  box-shadow:
    0 60px 120px rgba(0, 0, 0, 0.35),
    0 0 100px rgba(34, 211, 238, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Professional Image Overlay */
.professional-image-overlay {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
}

.professional-image-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(34, 211, 238, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 50%,
    rgba(251, 146, 60, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.professional-image-overlay:hover::before {
  opacity: 1;
}

.professional-image-overlay img {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.professional-image-overlay:hover img {
  transform: scale(1.1);
}

/* ULTRA PROFESSIONAL Section System - Maximum Display Utilization */
.professional-section {
  padding: 8rem 2rem;
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.professional-section-content {
  width: 100%;
  max-width: 100%;
  padding: 0 2rem;
}

@media (min-width: 640px) {
  .professional-section {
    padding: 10rem 3rem;
  }

  .professional-section-content {
    padding: 0 3rem;
  }
}

@media (min-width: 768px) {
  .professional-section {
    padding: 12rem 4rem;
  }

  .professional-section-content {
    padding: 0 4rem;
  }
}

@media (min-width: 1024px) {
  .professional-section {
    padding: 15rem 5rem;
  }

  .professional-section-content {
    padding: 0 5rem;
  }
}

@media (min-width: 1280px) {
  .professional-section {
    padding: 18rem 6rem;
  }

  .professional-section-content {
    padding: 0 6rem;
  }
}

@media (min-width: 1536px) {
  .professional-section {
    padding: 22rem 8rem;
  }

  .professional-section-content {
    padding: 0 8rem;
  }
}

@media (min-width: 1920px) {
  .professional-section {
    padding: 26rem 10rem;
  }

  .professional-section-content {
    padding: 0 10rem;
  }
}

/* ULTRA PROFESSIONAL Grid System - Maximum Impact Layout */
.professional-grid {
  display: grid;
  gap: 3rem;
  grid-template-columns: 1fr;
  width: 100%;
  max-width: none;
  place-items: center;
}

.professional-grid-2 {
  display: grid;
  gap: 3rem;
  grid-template-columns: 1fr;
  width: 100%;
  max-width: none;
  place-items: center;
}

.professional-grid-3 {
  display: grid;
  gap: 3rem;
  grid-template-columns: 1fr;
  width: 100%;
  max-width: none;
  place-items: center;
}

@media (min-width: 640px) {
  .professional-grid {
    gap: 3.5rem;
  }

  .professional-grid-2 {
    gap: 3.5rem;
  }

  .professional-grid-3 {
    gap: 3.5rem;
  }
}

@media (min-width: 768px) {
  .professional-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 4rem;
  }

  .professional-grid-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 4rem;
  }

  .professional-grid-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: 4rem;
  }
}

@media (min-width: 1024px) {
  .professional-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 4.5rem;
  }

  .professional-grid-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 4.5rem;
  }

  .professional-grid-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: 4.5rem;
  }
}

@media (min-width: 1280px) {
  .professional-grid {
    gap: 5rem;
  }

  .professional-grid-2 {
    gap: 5rem;
  }

  .professional-grid-3 {
    gap: 5rem;
  }
}

@media (min-width: 1536px) {
  .professional-grid {
    gap: 5.5rem;
  }

  .professional-grid-2 {
    gap: 5.5rem;
  }

  .professional-grid-3 {
    gap: 5.5rem;
  }
}

@media (min-width: 1920px) {
  .professional-grid {
    gap: 6rem;
  }

  .professional-grid-2 {
    gap: 6rem;
  }

  .professional-grid-3 {
    gap: 6rem;
  }
}

/* Professional Typography - Large & Impactful */
.professional-heading {
  font-size: 4rem;
  font-weight: 900;
  line-height: 0.9;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.02em;
}

@media (min-width: 640px) {
  .professional-heading {
    font-size: 5rem;
    margin-bottom: 2.5rem;
  }
}

@media (min-width: 768px) {
  .professional-heading {
    font-size: 6.5rem;
    margin-bottom: 3rem;
  }
}

@media (min-width: 1024px) {
  .professional-heading {
    font-size: 8rem;
    margin-bottom: 3.5rem;
  }
}

@media (min-width: 1280px) {
  .professional-heading {
    font-size: 9.5rem;
    margin-bottom: 4rem;
  }
}

@media (min-width: 1536px) {
  .professional-heading {
    font-size: 11rem;
    margin-bottom: 4.5rem;
  }
}

/* Professional Button Styles */
.professional-button {
  background: linear-gradient(135deg, #22d3ee, #a855f7);
  border: none;
  border-radius: 1rem;
  color: white;
  font-weight: 700;
  padding: 1rem 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.professional-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.professional-button:hover::before {
  left: 100%;
}

.professional-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(34, 211, 238, 0.3);
}

/* Professional Loading Animation */
.professional-loading {
  position: relative;
  overflow: hidden;
}

.professional-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: professional-shimmer 2s infinite;
}

@keyframes professional-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Full Width Layout Fixes */
.full-width-container {
  width: 100vw;
  max-width: none !important;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (min-width: 768px) {
  .full-width-container {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1024px) {
  .full-width-container {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

/* Ultra Large Text Sizes */
.text-10xl {
  font-size: 9rem;
  line-height: 1;
}

.text-11xl {
  font-size: 10rem;
  line-height: 1;
}

.text-12xl {
  font-size: 12rem;
  line-height: 1;
}

@media (min-width: 1280px) {
  .text-10xl {
    font-size: 11rem;
  }

  .text-11xl {
    font-size: 13rem;
  }

  .text-12xl {
    font-size: 15rem;
  }
}

@media (min-width: 1536px) {
  .text-10xl {
    font-size: 13rem;
  }

  .text-11xl {
    font-size: 15rem;
  }

  .text-12xl {
    font-size: 18rem;
  }
}

/* Professional Background Animation Styles */
.bg-animation-primary {
  filter: hue-rotate(200deg) saturate(0.8);
  animation: infinite-loop 20s linear infinite;
}

.bg-animation-secondary {
  filter: brightness(0.7) contrast(1.2);
  animation: infinite-loop 15s linear infinite reverse;
}

.bg-animation-tertiary {
  filter: hue-rotate(120deg) brightness(0.8);
  animation: infinite-loop 25s linear infinite;
}

@keyframes infinite-loop {
  0% {
    transform: translateX(0) scale(1);
  }
  25% {
    transform: translateX(-2px) scale(1.01);
  }
  50% {
    transform: translateX(0) scale(1.02);
  }
  75% {
    transform: translateX(2px) scale(1.01);
  }
  100% {
    transform: translateX(0) scale(1);
  }
}

/* Professional Image Styles */
.professional-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.professional-bg-image:hover {
  transform: scale(1.05);
}

/* ULTRA ENHANCED Professional Gradients - Unique Tab Identities */
.gradient-overlay-primary {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.92) 0%,
    rgba(88, 28, 135, 0.88) 20%,
    rgba(30, 58, 138, 0.90) 40%,
    rgba(6, 182, 212, 0.85) 60%,
    rgba(88, 28, 135, 0.88) 80%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 25s ease infinite;
}

.gradient-overlay-services {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.92) 0%,
    rgba(59, 130, 246, 0.88) 25%,
    rgba(88, 28, 135, 0.90) 50%,
    rgba(147, 51, 234, 0.85) 75%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 20s ease infinite;
}

.gradient-overlay-portfolio {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.92) 0%,
    rgba(147, 51, 234, 0.88) 20%,
    rgba(6, 182, 212, 0.90) 40%,
    rgba(34, 211, 238, 0.85) 60%,
    rgba(30, 58, 138, 0.88) 80%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 30s ease infinite;
}

.gradient-overlay-about {
  background: linear-gradient(135deg,
    rgba(6, 182, 212, 0.92) 0%,
    rgba(34, 211, 238, 0.88) 25%,
    rgba(168, 85, 247, 0.90) 50%,
    rgba(251, 146, 60, 0.85) 75%,
    rgba(88, 28, 135, 0.92) 100%
  );
  animation: gradientShift 22s ease infinite;
}

.gradient-overlay-technologies {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.92) 0%,
    rgba(16, 185, 129, 0.88) 20%,
    rgba(6, 182, 212, 0.90) 40%,
    rgba(168, 85, 247, 0.85) 60%,
    rgba(147, 51, 234, 0.88) 80%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 18s ease infinite;
}

.gradient-overlay-testimonials {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.92) 0%,
    rgba(245, 101, 101, 0.88) 25%,
    rgba(236, 72, 153, 0.90) 50%,
    rgba(168, 85, 247, 0.85) 75%,
    rgba(88, 28, 135, 0.92) 100%
  );
  animation: gradientShift 28s ease infinite;
}

.gradient-overlay-pricing {
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.92) 0%,
    rgba(147, 51, 234, 0.88) 20%,
    rgba(236, 72, 153, 0.90) 40%,
    rgba(251, 146, 60, 0.85) 60%,
    rgba(34, 197, 94, 0.88) 80%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 24s ease infinite;
}

.gradient-overlay-faq {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.92) 0%,
    rgba(129, 140, 248, 0.88) 25%,
    rgba(168, 85, 247, 0.90) 50%,
    rgba(6, 182, 212, 0.85) 75%,
    rgba(30, 58, 138, 0.92) 100%
  );
  animation: gradientShift 26s ease infinite;
}

.gradient-overlay-contact {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.92) 0%,
    rgba(245, 101, 101, 0.88) 20%,
    rgba(34, 197, 94, 0.90) 40%,
    rgba(6, 182, 212, 0.85) 60%,
    rgba(168, 85, 247, 0.88) 80%,
    rgba(15, 23, 42, 0.92) 100%
  );
  animation: gradientShift 32s ease infinite;
}

/* Ensure proper content alignment */
body {
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

/* Professional responsive containers */
.professional-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .professional-container {
    padding: 0 2rem;
  }
}

@media (min-width: 768px) {
  .professional-container {
    padding: 0 3rem;
  }
}

@media (min-width: 1024px) {
  .professional-container {
    padding: 0 4rem;
  }
}

@media (min-width: 1280px) {
  .professional-container {
    padding: 0 5rem;
  }
}

@media (min-width: 1536px) {
  .professional-container {
    padding: 0 6rem;
  }
}
